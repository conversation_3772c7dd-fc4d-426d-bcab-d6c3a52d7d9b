
import { getSettings } from "../utils/settings";
import { parseLocal } from "../parsers/localParser";
import { parseWithGemini } from "../parsers/geminiParser";
import { insertEvent } from "../utils/calendarApi";

chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  (async () => {
    if (message.action === "parseEvent") {
      const settings = await getSettings();
      let parsed = parseLocal(message.text, settings.defaultDuration);
      if (parsed.confidence < 0.5 && settings.enableCloudParsing) {
        parsed = await parseWithGemini(message.text, settings.defaultDuration);
      }
      if (!parsed.start) {
        const now = new Date();
        const start = new Date(now.getTime() + 5 * 60000);
        const end = new Date(start.getTime() + settings.defaultDuration * 60000);
        const fmt = (d: Date) => {
          const pad = (n: number) => String(n).padStart(2, "0");
          return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())}T${pad(d.getHours())}:${pad(d.getMinutes())}`;
        };
        parsed.start = fmt(start);
        parsed.end = fmt(end);
      }
      sendResponse({ parsed });
    }

    if (message.action === "createEvent") {
      try {
        const result = await insertEvent(message.event);
        if (result.id) {
          const url = `https://calendar.google.com/calendar/u/0/r/eventedit/${encodeURIComponent(result.id)}`;
          chrome.tabs.create({ url });
        } else if (message.event?.start) {
          const date = new Date(message.event.start);
          const y = date.getFullYear();
          const m = String(date.getMonth()+1).padStart(2, "0");
          const d = String(date.getDate()).padStart(2, "0");
          const url = `https://calendar.google.com/calendar/u/0/r/day/${y}/${m}/${d}`;
          chrome.tabs.create({ url });
        }
        sendResponse({ success: true });
      } catch (e) {
        console.error(e);
        sendResponse({ success: false, error: String(e) });
      }
    }
  })();
  return true;
});
