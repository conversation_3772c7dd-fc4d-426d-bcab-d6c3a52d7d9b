
import type { ParsedEvent } from "../types/eventTypes";

/**
 * Stub for Gemini parsing — replace with Google AI SDK call.
 */
export async function parseWithGemini(input: string, defaultDuration: number): Promise<ParsedEvent> {
  const now = new Date();
  const start = new Date(now.getTime() + 60 * 60 * 1000);
  const end = new Date(start.getTime() + defaultDuration * 60000);
  const fmt = (d: Date) => {
    const pad = (n: number) => String(n).padStart(2, "0");
    return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())}T${pad(d.getHours())}:${pad(d.getMinutes())}`;
  };
  return {
    title: input.slice(0, 80) || "New Event",
    start: fmt(start),
    end: fmt(end),
    source: "gemini",
    confidence: 0.95
  };
}
