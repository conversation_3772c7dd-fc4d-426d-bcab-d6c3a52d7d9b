
<script lang="ts">
  import type { ParsedEvent } from "../types/eventTypes";
  import { createEventDispatcher } from "svelte";
  export let event: ParsedEvent;
  const dispatch = createEventDispatcher();
  function create() { dispatch("create", { event }); }
  function close() { dispatch("close"); }
</script>

<style>
  .modal { position: fixed; inset: 0; background: rgba(0,0,0,.35);
    display:flex; align-items:center; justify-content:center; z-index:999999; }
  .card { background:white; width: 480px; border-radius:12px; padding:16px; box-shadow:0 8px 24px rgba(0,0,0,.2); }
  input, textarea { width:100%; margin:6px 0; padding:8px; }
  .row { display:flex; gap:8px; justify-content:flex-end; margin-top:8px; }
  button { padding:8px 12px; border-radius:8px; border:none; cursor:pointer; }
  button.primary { background:#1a73e8; color:white; }
  .meta { font-size:12px; color:#666; }
</style>

<div class="modal" on:click={close}>
  <div class="card" on:click|stopPropagation>
    <h3>Confirm Event Details</h3>
    <div class="meta">Parsed via: {event.source} · confidence {Math.round(event.confidence*100)}%</div>

    <label>Title</label>
    <input bind:value={event.title} />

    <label>Start</label>
    <input type="datetime-local" bind:value={event.start} />

    <label>End</label>
    <input type="datetime-local" bind:value={event.end} />

    <label>Location</label>
    <input bind:value={event.location} />

    <label>Notes</label>
    <textarea bind:value={event.notes}></textarea>

    {#if event.attendees && event.attendees.length}
      <label>Attendees</label>
      <ul>
        {#each event.attendees as a}
          <li>{a.email}</li>
        {/each}
      </ul>
    {/if}

    <div class="row">
      <button on:click={close}>Cancel</button>
      <button class="primary" on:click={create}>Create Event</button>
    </div>
  </div>
</div>
