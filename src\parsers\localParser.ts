
import chrono from "chrono-node";
import type { ParsedEvent } from "../types/eventTypes";

const emailRegex = /[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}/gi;

export function parseLocal(input: string, defaultDuration: number): ParsedEvent {
  const parsedDate = chrono.parseDate(input);
  if (!parsedDate) {
    return {
      title: safeTitle(input),
      start: "",
      end: "",
      attendees: extractEmails(input),
      notes: "",
      source: "local",
      confidence: 0
    };
  }

  const startIso = toLocalISOString(parsedDate);
  const endIso = toLocalISOString(new Date(parsedDate.getTime() + defaultDuration * 60000));

  return {
    title: deriveTitle(input),
    start: startIso,
    end: endIso,
    location: extractLocation(input),
    attendees: extractEmails(input),
    notes: "",
    source: "local",
    confidence: 0.9
  };
}

function toLocalISOString(date: Date): string {
  const pad = (n: number) => String(n).padStart(2, "0");
  const yyyy = date.getFullYear();
  const mm = pad(date.getMonth() + 1);
  const dd = pad(date.getDate());
  const hh = pad(date.getHours());
  const mi = pad(date.getMinutes());
  return `${yyyy}-${mm}-${dd}T${hh}:${mi}`;
}

function safeTitle(text: string): string {
  return text.trim().slice(0, 140) || "New Event";
}

function deriveTitle(text: string): string {
  const cleaned = text.replace(/\b(on|at|with|in|from|for)\b.*$/i, "").trim();
  return safeTitle(cleaned.length ? cleaned : text);
}

function extractEmails(text: string): { email: string }[] {
  const matches = text.match(emailRegex) || [];
  const unique = Array.from(new Set(matches.map(e => e.toLowerCase())));
  return unique.map(email => ({ email }));
}

function extractLocation(text: string): string | undefined {
  const m = text.match(/\b(?:at|in)\s+([^,.;\n]+)\b/i);
  return m ? m[1].trim() : undefined;
}
