
<script lang="ts">
  import type { Settings } from "../types/eventTypes";
  import { getSettings, saveSettings } from "../utils/settings";

  let settings: Settings = {
    defaultDuration: 60,
    autoInvite: true,
    showSummary: true,
    enableCloudParsing: false
  };

  async function load() {
    settings = await getSettings();
  }
  async function persist() {
    await saveSettings(settings);
    alert("Settings saved");
  }
  load();
</script>

<style>
  body, :global(html), :global(body) { font-family: system-ui, sans-serif; }
  .wrap { max-width: 600px; margin: 24px auto; padding: 16px; }
  .row { margin: 12px 0; }
  label { display:block; font-weight:600; margin-bottom:6px; }
  select, input[type="checkbox"] { padding: 6px; }
  button { margin-top: 16px; padding: 8px 12px; border: none; border-radius: 8px; background: #1a73e8; color: white; cursor: pointer; }
</style>

<div class="wrap">
  <h2>AI Event Creator — Settings</h2>

  <div class="row">
    <label>Default Duration (minutes)</label>
    <select bind:value={settings.defaultDuration}>
      <option value="15">15</option>
      <option value="30">30</option>
      <option value="45">45</option>
      <option value="60">60</option>
      <option value="90">90</option>
      <option value="120">120</option>
    </select>
  </div>

  <div class="row">
    <label><input type="checkbox" bind:checked={settings.autoInvite} /> Auto-invite detected attendees</label>
  </div>

  <div class="row">
    <label><input type="checkbox" bind:checked={settings.showSummary} /> Show summary before creating</label>
  </div>

  <div class="row">
    <label><input type="checkbox" bind:checked={settings.enableCloudParsing} /> Enable cloud parsing (Gemini)</label>
  </div>

  <button on:click={persist}>Save</button>
</div>
