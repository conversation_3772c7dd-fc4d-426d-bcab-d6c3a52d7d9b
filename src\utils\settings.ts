
import type { Settings } from "../types/eventTypes";
import { DEFAULT_SETTINGS } from "../types/eventTypes";

export async function getSettings(): Promise<Settings> {
  return new Promise(resolve => {
    chrome.storage.sync.get(DEFAULT_SETTINGS, (data) => resolve(data as Settings));
  });
}

export async function saveSettings(settings: Settings): Promise<void> {
  return new Promise(resolve => {
    chrome.storage.sync.set(settings, () => resolve());
  });
}
