
import type { ParsedEvent } from "../types/eventTypes";
import { getAccessToken } from "./auth";

export async function insertEvent(ev: ParsedEvent): Promise<{ id?: string }> {
  const token = await getAccessToken();
  if (!token) {
    console.warn("No OAuth token (stub). Skipping Calendar insert.");
    return { id: undefined };
  }

  const body = {
    summary: ev.title,
    location: ev.location,
    description: ev.notes,
    start: { dateTime: new Date(ev.start).toISOString() },
    end: { dateTime: new Date(ev.end).toISOString() },
    attendees: ev.attendees,
    conferenceData: { createRequest: { requestId: crypto.randomUUID() } }
  };

  const res = await fetch("https://www.googleapis.com/calendar/v3/calendars/primary/events?conferenceDataVersion=1", {
    method: "POST",
    headers: { "Authorization": `Bearer ${token}`, "Content-Type": "application/json" },
    body: JSON.stringify(body)
  });
  if (!res.ok) throw new Error(`Calendar API error: ${res.status} ${await res.text()}`);
  const data = await res.json();
  return { id: data.id };
}
