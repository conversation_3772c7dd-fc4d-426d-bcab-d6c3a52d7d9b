
<script lang="ts">
  import { createEventDispatcher } from "svelte";
  const dispatch = createEventDispatcher();
  let inputText = "";
  function submit() { if (inputText.trim().length) dispatch("submit", { text: inputText }); }
  function close() { dispatch("close"); }
</script>

<style>
  .modal { position: fixed; inset: 0; background: rgba(0,0,0,.35);
    display:flex; align-items:center; justify-content:center; z-index:999999; }
  .card { background:white; width: 440px; border-radius:12px; padding:16px; box-shadow:0 8px 24px rgba(0,0,0,.2); }
  textarea { width:100%; height:120px; padding:8px; }
  .row { display:flex; gap:8px; justify-content:flex-end; margin-top:8px; }
  button { padding:8px 12px; border-radius:8px; border:none; cursor:pointer; }
  button.primary { background:#1a73e8; color:white; }
</style>

<div class="modal" on:click={close}>
  <div class="card" on:click|stopPropagation>
    <h3>AI Event Creator</h3>
    <p>Describe your event in natural language.</p>
    <textarea bind:value={inputText} placeholder="e.g., Lunch this Sat at <NAME_EMAIL> at Cafe Luna"></textarea>
    <div class="row">
      <button on:click={close}>Cancel</button>
      <button class="primary" on:click={submit}>Parse</button>
    </div>
  </div>
</div>
