
export type ParsedEvent = {
  title: string;
  start: string; // for <input type="datetime-local">
  end: string;
  location?: string;
  attendees?: { email: string }[];
  notes?: string;
  source: "local" | "gemini";
  confidence: number;
};

export type Settings = {
  defaultDuration: number;
  autoInvite: boolean;
  showSummary: boolean;
  enableCloudParsing: boolean;
};

export const DEFAULT_SETTINGS: Settings = {
  defaultDuration: 60,
  autoInvite: true,
  showSummary: true,
  enableCloudParsing: false
};
