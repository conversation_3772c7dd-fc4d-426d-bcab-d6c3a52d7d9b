
import InputModal from '../modals/InputModal.svelte';
import SummaryModal from '../modals/SummaryModal.svelte';
import type { ParsedEvent } from '../types/eventTypes';
import { getSettings } from '../utils/settings';

let inputModal: any = null;
let summaryModal: any = null;

function ensureIcon() {
  if (document.getElementById('ai-gemini-button')) return;
  const btn = document.createElement('button');
  btn.id = 'ai-gemini-button';
  btn.textContent = '✨';
  Object.assign(btn.style, {
    position: 'fixed', right: '18px', bottom: '18px', zIndex: '999999',
    borderRadius: '24px', width: '48px', height: '48px', border: 'none',
    cursor: 'pointer', fontSize: '20px', boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
    background: '#1a73e8', color: '#fff'
  } as CSSStyleDeclaration);
  btn.title = 'AI Event Creator';
  btn.addEventListener('click', openInputModal);
  document.body.appendChild(btn);
}

function openInputModal() {
  closeModals();
  const host = document.createElement('div');
  document.body.appendChild(host);
  inputModal = new InputModal({ target: host, props: {} });
  inputModal.$on('submit', async (e: any) => {
    const text = e.detail.text as string;
    const parsed = await parseText(text);
    const settings = await getSettings();
    if (settings.showSummary) openSummaryModal(parsed);
    else await createEvent(parsed);
    closeInputModal();
  });
  inputModal.$on('close', closeInputModal);
}

function openSummaryModal(event: ParsedEvent) {
  const host = document.createElement('div');
  document.body.appendChild(host);
  summaryModal = new SummaryModal({ target: host, props: { event } });
  summaryModal.$on('create', async (e: any) => {
    await createEvent(e.detail.event as ParsedEvent);
    closeSummaryModal();
  });
  summaryModal.$on('close', closeSummaryModal);
}

async function parseText(text: string): Promise<ParsedEvent> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'parseEvent', text }, (resp) => resolve(resp.parsed as ParsedEvent));
  });
}

async function createEvent(ev: ParsedEvent): Promise<void> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'createEvent', event: ev }, () => resolve());
  });
}

function closeInputModal() { if (inputModal) { inputModal.$destroy(); inputModal = null; } }
function closeSummaryModal() { if (summaryModal) { summaryModal.$destroy(); summaryModal = null; } }
function closeModals() { closeInputModal(); closeSummaryModal(); }

ensureIcon();
