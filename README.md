
# AI Event Creator for Google Calendar (Chrome MV3 + Svelte)

Create Google Calendar events from natural language with local parsing and optional Gemini fallback.

## Install & Build

1. Run `npm install`
2. Run `npm run build`
3. Load the **dist** folder at `chrome://extensions` (enable Developer mode → Load unpacked).

## Features
- Svelte UI modals injected into Google Calendar
- Local parsing with `chrono-node`
- Optional cloud parsing (Gemini) stub
- Settings: default duration, auto-invite, summary screen, cloud parsing toggle

## Notes
- OAuth is stubbed — implement `src/utils/auth.ts` for Google Identity Services.
- `insertEvent` uses Calendar API v3; ensure scopes & verification for public release.
